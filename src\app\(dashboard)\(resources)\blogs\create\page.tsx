'use client';

import React, { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import dynamic from 'next/dynamic';
import { FileUpload } from '@/modules/product/component/fileupload';
import { CollapsibleSection } from '@/modules/product/component/collapsible';
import { useCreateBlogs } from '@/modules/blogs/mutations/use-create-blog';
import { IBlog } from '@/types/blogs';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

const CkEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false });

const CreateBlogPage = () => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const createBlogMutation = useCreateBlogs();

  const [formData, setFormData] = useState<IBlog>({
    id: '',
    title: '',
    slug: '',
    description: '',
    content: '',
    image: '',
    published: false,
  });

  // Generate slug from title
  const generateSlug = (title: string) =>
    title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)+/g, '');

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value, type } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
    }));

    if (name === 'title') {
      setFormData((prev) => ({
        ...prev,
        slug: generateSlug(value),
      }));
    }
  };

  const handleContentChange = (data: string) => {
    setFormData((fd) => ({ ...fd, content: data }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    try {
      const payload: IBlog = {
        id: '',
        title: formData.title,
        slug: formData.slug,
        description: formData.description,
        content: formData.content,
        image: formData.image,
        published: formData.published,
      };

      await createBlogMutation.mutateAsync(payload);
      alert('Blog created successfully');
      router.push('/blogs');
    } catch (error) {
      console.error('Error creating blog:', error);
      alert('Error creating blog. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="container mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <Link href="/blogs" className="text-brand hover:text-brand/80">
            ← Back to Blogs
          </Link>
          <h1 className="text-2xl font-bold text-brand">Create Blog</h1>
        </div>

        <div className="space-y-6">
          <CollapsibleSection title="Blog Content" isOpen={true} onToggle={() => {}}>
            <div>
              <div className="bg-white p-4 shadow-lg">
                <h2 className="text-base font-medium mb-4">Blog Details</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label
                        htmlFor="title"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Title *
                      </label>
                      <Input
                        type="text"
                        id="title"
                        name="title"
                        required
                        value={formData.title}
                        onChange={handleInputChange}
                        placeholder="Enter Blog Title"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="slug"
                        className="block text-sm font-medium text-gray-700 mb-2"
                      >
                        Slug *
                      </label>
                      <Input
                        type="text"
                        id="slug"
                        name="slug"
                        required
                        value={formData.slug}
                        onChange={handleInputChange}
                        placeholder="Enter Blog Slug"
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                  </div>

                  <div>
                    <label
                      htmlFor="description"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Short Description *
                    </label>
                    <Textarea
                      id="description"
                      name="description"
                      required
                      rows={3}
                      value={formData.description}
                      onChange={handleInputChange}
                      placeholder="Enter a brief description of your blog post..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
                    />
                  </div>

                  <div>
                    <label
                      htmlFor="content"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Content *
                    </label>
                    <CkEditor value={formData.content} onChange={handleContentChange} />
                  </div>

                  {/* <div>
                    <label
                      htmlFor="imageAltTag"
                      className="block text-sm font-medium text-gray-700 mb-2"
                    >
                      Image Alt Tag
                    </label>
                    <input
                      type="text"
                      id="imageAltTag"
                      name="imageAltTag"
                      value={formData.imageAltTag}
                      onChange={handleInputChange}
                      placeholder="Enter Image Alt Tag"
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    />
                  </div> */}

                  <div>
                    <FileUpload
                      label="Banner Image"
                      accept="image/*"
                      onFileChange={(file) => setFormData((prev) => ({ ...prev, image: file }))}
                      showPreview
                      previewSrc={formData.image ? URL.createObjectURL(formData.image) : undefined}
                      previewAlt="Banner Image Preview"
                    />
                  </div>

                  <div>
                    <div className="flex items-center space-x-3">
                      <input
                        type="checkbox"
                        id="published"
                        name="published"
                        checked={formData.published}
                        onChange={handleInputChange}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label
                        htmlFor="published"
                        className="text-sm font-medium text-gray-700"
                      >
                        Publish immediately
                      </label>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      If unchecked, the blog will be saved as a draft
                    </p>
                  </div>

                  <div className="flex justify-end space-x-4">
                    <Link href="/blogs">
                      <Button type="button" variant="outline" className="px-6">
                        Cancel
                      </Button>
                    </Link>
                    <Button
                      type="submit"
                      disabled={isLoading}
                      className="bg-brand hover:bg-brand/80 px-6"
                    >
                      {isLoading ? 'Creating...' : 'Submit'}
                    </Button>
                  </div>
                </form>
              </div>
            </div>
          </CollapsibleSection>
        </div>
      </div>
    </div>
  );
};

export default CreateBlogPage;



// 'use client';

// import React, { useState } from 'react';
// import Link from 'next/link';
// import { useRouter } from 'next/navigation';
// import { Button } from '@/components/ui/button';
// import dynamic from 'next/dynamic';
// import { SeoDetailsSection } from '@/components/package/seo-detail-section';
// import { SchemaDetailsSection } from '@/components/package/seo-schema-detail-section';
// import { FileUpload } from '@/modules/product/component/fileupload';
// import { CollapsibleSection } from '@/modules/product/component/collapsible';

// const CkEditor = dynamic(() => import('@/utils/ck-editor'), { ssr: false });

// import { BlogFormData } from '@/types/blogs';

// const CreateBlogPage = () => {
//   const router = useRouter();
//   const [isLoading, setIsLoading] = useState(false);
//   const [openSeo, setOpenSeo] = useState(true);
//   const [openSchema, setOpenSchema] = useState(false);
//   const [openContent, setOpenContent] = useState(true);
//   const [schema, setSchema] = useState('');

//   const [formData, setFormData] = useState<BlogFormData>({
//     title: '',
//     slug: '',
//     description: '',
//     content: '',
//     imageAltTag: '',
//     image: null,
//     published: false,
//     metaTitle: '',
//     metaDescription: '',
//     metaKeywords: '',
//     canonicalUrl: '',
//     schema: '',
//   });

//   const generateSlug = (title: string) =>
//     title
//       .toLowerCase()
//       .replace(/[^a-z0-9]+/g, '-')
//       .replace(/(^-|-$)+/g, '');

//   const handleInputChange = (
//     e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
//   ) => {
//     const { name, value, type } = e.target;
//     setFormData((prev) => ({
//       ...prev,
//       [name]:
//         type === 'checkbox' ? (e.target as HTMLInputElement).checked : value,
//     }));

//     if (name === 'title') {
//       setFormData((prev) => ({
//         ...prev,
//         slug: generateSlug(value),
//       }));
//     }
//   };

//   const handleContentChange = (data: string) => {
//     setFormData((fd) => ({ ...fd, content: data }));
//   };

//   const handleSubmit = async (e: React.FormEvent) => {
//     e.preventDefault();
//     setIsLoading(true);

//     try {
//       const submitData = new FormData();
//       submitData.append('title', formData.title);
//       submitData.append('slug', formData.slug);
//       submitData.append('description', formData.description);
//       submitData.append('content', formData.content);
//       submitData.append('imageAltTag', formData.imageAltTag);
//       submitData.append('published', formData.published.toString());

//       submitData.append('metaTitle', formData.metaTitle ?? '');
//       submitData.append('metaDescription', formData.metaDescription ?? '');
//       submitData.append('metaKeywords', formData.metaKeywords ?? '');
//       submitData.append('canonicalUrl', formData.canonicalUrl ?? '');

//       submitData.append('schema', formData.schema ?? '');

//       if (formData.image) {
//         submitData.append('image', formData.image);
//       }

//       console.log('Submitting blog data:', {
//         ...Object.fromEntries(submitData),
//       });

//       await new Promise((resolve) => setTimeout(resolve, 1000));

//       router.push('/blogs');
//     } catch (error) {
//       console.error('Error creating blog:', error);
//       alert('Error creating blog. Please try again.');
//     } finally {
//       setIsLoading(false);
//     }
//   };

//   return (
//     <div className="min-h-screen bg-gray-50 p-8">
//       <div className="container mx-auto">
//         {/* Header */}
//         <div className="flex items-center gap-4 mb-8">
//           <Link href="/blogs" className="text-brand hover:text-brand/80">
//             ← Back to Blogs
//           </Link>
//           <h1 className="text-2xl font-bold text-brand">Create Blog</h1>
//         </div>

//         <div className="space-y-6">
//           {/* <CollapsibleSection
//             title="SEO Details"
//             isOpen={openSeo}
//             onToggle={() => setOpenSeo((v) => !v)}
//           > */}
//           {/* <SeoDetailsSection
//                             formData={{
//                                 metaTitle: formData.metaTitle,
//                                 metaDescription: formData.metaDescription,
//                                 metaKeywords: formData.metaKeywords,
//                                 canonicalUrl: formData.canonicalUrl,
//                             }}
//                             setFormData={(seoChanges) =>
//                                 setFormData(fd => ({ ...fd, ...seoChanges }))
//                             }
//                         /> */}
//           {/* </CollapsibleSection> */}

//           <CollapsibleSection
//             title="Schema Details"
//             isOpen={openSchema}
//             onToggle={() => setOpenSchema((v) => !v)}
//           >
//             <SchemaDetailsSection schema={schema} setSchema={setSchema} />
//           </CollapsibleSection>

//           <CollapsibleSection
//             title="Blog Content"
//             isOpen={openContent}
//             onToggle={() => setOpenContent((v) => !v)}
//           >
//             <div>
//               <div className="bg-white p-4 shadow-lg">
//                 <h2 className="text-base font-medium mb-4">Blog Details</h2>
//                 <form onSubmit={handleSubmit} className="space-y-6">
//                   <div className="bg-white">
//                     <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
//                       <div>
//                         <label
//                           htmlFor="title"
//                           className="block text-sm font-medium text-gray-700 mb-2"
//                         >
//                           Title *
//                         </label>
//                         <input
//                           type="text"
//                           id="title"
//                           name="title"
//                           required
//                           value={formData.title}
//                           onChange={handleInputChange}
//                           placeholder="Enter Blog Title"
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                         />
//                       </div>
//                       <div>
//                         <label
//                           htmlFor="slug"
//                           className="block text-sm font-medium text-gray-700 mb-2"
//                         >
//                           Slug *
//                         </label>
//                         <input
//                           type="text"
//                           id="slug"
//                           name="slug"
//                           required
//                           value={formData.slug}
//                           onChange={handleInputChange}
//                           placeholder="Enter Blog Slug"
//                           className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                         />
//                       </div>
//                     </div>
//                   </div>

//                   {/* Short Description */}
//                   <div className="bg-white">
//                     <label
//                       htmlFor="description"
//                       className="block text-sm font-medium text-gray-700 mb-2"
//                     >
//                       Short Description *
//                     </label>
//                     <textarea
//                       id="description"
//                       name="description"
//                       required
//                       rows={3}
//                       value={formData.description}
//                       onChange={handleInputChange}
//                       placeholder="Enter a brief description of your blog post..."
//                       className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-vertical"
//                     />
//                   </div>

//                   {/* Content */}
//                   <div className="bg-white">
//                     <label
//                       htmlFor="content"
//                       className="block text-sm font-medium text-gray-700 mb-2"
//                     >
//                       Content *
//                     </label>
//                     <CkEditor
//                       value={formData.content}
//                       onChange={handleContentChange}
//                     />
//                   </div>

//                   {/* Image Alt Tag */}
//                   <div className="bg-white">
//                     <label
//                       htmlFor="imageAltTag"
//                       className="block text-sm font-medium text-gray-700 mb-2"
//                     >
//                       Image Alt Tag
//                     </label>
//                     <input
//                       type="text"
//                       id="imageAltTag"
//                       name="imageAltTag"
//                       value={formData.imageAltTag}
//                       onChange={handleInputChange}
//                       placeholder="Enter Image Alt Tag"
//                       className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
//                     />
//                   </div>

//                   {/* Banner Image */}
//                   <div className="bg-white">
//                     <FileUpload
//                       label="Banner Image"
//                       accept="image/*"
//                       onFileChange={(file) =>
//                         setFormData((prev) => ({ ...prev, image: file }))
//                       }
//                       showPreview={true}
//                       previewSrc={
//                         formData.image
//                           ? URL.createObjectURL(formData.image)
//                           : undefined
//                       }
//                       previewAlt="Banner Image Preview"
//                     />
//                   </div>

//                   {/* Publish Checkbox */}
//                   <div className="bg-white">
//                     <div className="flex items-center space-x-3">
//                       <input
//                         type="checkbox"
//                         id="published"
//                         name="published"
//                         checked={formData.published}
//                         onChange={handleInputChange}
//                         className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
//                       />
//                       <label
//                         htmlFor="published"
//                         className="text-sm font-medium text-gray-700"
//                       >
//                         Publish immediately
//                       </label>
//                     </div>
//                     <p className="text-xs text-gray-500 mt-1">
//                       If unchecked, the blog will be saved as a draft
//                     </p>
//                   </div>

//                   {/* Submit */}
//                   <div className="flex justify-end space-x-4 bg-white">
//                     <Link href="/blogs">
//                       <Button type="button" variant="outline" className="px-6">
//                         Cancel
//                       </Button>
//                     </Link>
//                     <Button
//                       type="submit"
//                       disabled={isLoading}
//                       className="bg-brand hover:bg-brand/80 px-6"
//                     >
//                       {isLoading ? (
//                         <>
//                           <svg
//                             className="animate-spin -ml-1 mr-3 h-4 w-4 text-white"
//                             viewBox="0 0 24 24"
//                             fill="none"
//                             xmlns="http://www.w3.org/2000/svg"
//                           >
//                             <circle
//                               className="opacity-25"
//                               cx="12"
//                               cy="12"
//                               r="10"
//                               stroke="currentColor"
//                               strokeWidth="4"
//                             ></circle>
//                             <path
//                               className="opacity-75"
//                               fill="currentColor"
//                               d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
//                             ></path>
//                           </svg>
//                           Creating...
//                         </>
//                       ) : (
//                         'Submit'
//                       )}
//                     </Button>
//                   </div>
//                 </form>
//               </div>
//             </div>
//           </CollapsibleSection>
//         </div>
//       </div>
//     </div>
//   );
// };

// export default CreateBlogPage;
